# استمارة تقويم تقنية المعلومات

نظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات للصفوف من الأول حتى الثاني عشر.

## المميزات

### ✨ المميزات الرئيسية
- **إدارة شاملة للطلاب**: إضافة وتعديل وحذف بيانات الطلاب
- **استيراد من Excel**: رفع بيانات الطلاب من ملفات Excel
- **نظام درجات متقدم**: دعم أنظمة التقييم المختلفة للمراحل الدراسية
- **النتائج النهائية**: حساب المجموع الكلي ومتوسط الفصلين والتقديرات النهائية
- **تقارير احترافية**: تقارير فصلية وسنوية قابلة للطباعة
- **إحصائيات تفاعلية**: رسوم بيانية وتحليلات شاملة
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **دعم RTL**: واجهة مصممة خصيصاً للغة العربية

### 📊 أنظمة التقييم
#### الصفوف 1-4 (المجموع من 50 درجة)
- الأعمال الشفوية (فترتين × 10 درجات)
- الأنشطة العملية (فترتين × 10 درجات)
- المشروع (10 درجات)

#### الصفوف 5-10 (المجموع من 100 درجة)
- الأعمال الشفوية (فترتين × 10 درجات)
- الأنشطة العملية (فترتين × 20 درجة)
- المشروع (20 درجة)
- اختبار قصير (20 درجة)

#### الصفوف 11-12 (المجموع من 100 درجة)
- الأعمال الشفوية (فترتين × 5 درجات)
- الأنشطة العملية (فترتين × 20 درجة)
- المشروع (20 درجة)
- اختبار قصير (20 درجة)
- الاختبار النهائي (30 درجة)

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Database**: IndexedDB (SQLite في المتصفح)
- **Desktop**: Electron
- **Styling**: CSS Grid, Flexbox, RTL Support
- **Charts**: Chart.js
- **Excel**: SheetJS (XLSX)
- **Icons**: Font Awesome

## متطلبات النظام

### للتشغيل كتطبيق ويب
- متصفح حديث يدعم ES6+ و IndexedDB
- Chrome 60+, Firefox 55+, Safari 12+, Edge 79+

### للتشغيل كتطبيق سطح مكتب
- Windows 10+ (x64/x86)
- macOS 10.14+ (Intel/Apple Silicon)
- Linux Ubuntu 18.04+ (x64)

## التثبيت والتشغيل

### 1. تشغيل كتطبيق ويب

```bash
# استنساخ المشروع
git clone https://github.com/your-username/it-evaluation-system.git
cd it-evaluation-system

# تشغيل خادم محلي
npx http-server . -p 8080 -o
```

أو ببساطة افتح ملف `index.html` في المتصفح.

### 2. تشغيل كتطبيق Electron

```bash
# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm run dev

# أو التشغيل العادي
npm start
```

### 3. بناء تطبيق سطح المكتب

```bash
# بناء لجميع المنصات
npm run build

# بناء لـ Windows فقط
npm run build-win

# بناء لـ macOS فقط
npm run build-mac

# بناء لـ Linux فقط
npm run build-linux
```

## دليل الاستخدام

### 1. إعداد النظام
1. افتح التطبيق
2. انتقل إلى قسم "الإعدادات"
3. أدخل اسم المدرسة والعام الدراسي

### 2. إضافة الطلاب
#### الطريقة الأولى: الإدخال اليدوي
1. انتقل إلى قسم "إدارة الطلاب"
2. اضغط على "إضافة طالب"
3. أدخل البيانات المطلوبة

#### الطريقة الثانية: الاستيراد من Excel
1. انتقل إلى قسم "إدارة الطلاب"
2. اضغط على "استيراد من Excel"
3. حمل نموذج Excel أو استخدم ملفك الخاص
4. تأكد من وجود الأعمدة: رقم الطالب، اسم الطالب، الصف، الشعبة

### 3. إدخال الدرجات
1. انتقل إلى قسم "إدخال الدرجات"
2. اختر العام الدراسي والفصل والصف والشعبة
3. اضغط على "تحميل جدول الدرجات"
4. أدخل الدرجات في الجدول التفاعلي
5. احفظ الدرجات لكل طالب أو احفظ الكل مرة واحدة

### 4. عرض النتائج النهائية
1. انتقل إلى قسم "النتائج النهائية"
2. اختر العام الدراسي والصف والشعبة
3. اضغط على "عرض النتائج"
4. ستظهر النتائج مرتبة حسب المجموع الكلي مع:
   - مجموع الفصل الأول + الفصل الثاني
   - المجموع الكلي ومتوسط الفصلين
   - النسبة المئوية والتقدير النهائي
   - الترتيب العام للطلاب

### 5. إنشاء التقارير
1. انتقل إلى قسم "التقارير والإحصائيات"
2. اختر نوع التقرير (فصلي أو سنوي)
3. حدد المعايير المطلوبة
4. اطبع التقرير أو صدره إلى PDF/Excel

## الميزات المتقدمة

### 🎨 التصميم الاحترافي
- تصميم متجاوب يتكيف مع جميع الشاشات
- دعم كامل للغة العربية مع RTL
- ألوان متدرجة للمستويات مع حفظ الألوان في الطباعة
- خطوط عربية عالية الجودة

### 📄 نظام الطباعة المتقدم
- رأس احترافي مع التاريخ والوقت
- تذييل رسمي مع خانات التوقيع
- تخطيط متوازن عبر عرض الصفحة
- دعم الطباعة الملونة والأبيض والأسود

### 📊 الإحصائيات والتحليلات
- توزيع الدرجات حسب المستويات
- نسب النجاح والتفوق
- متوسطات الدرجات
- رسوم بيانية تفاعلية

### 💾 إدارة البيانات
- نسخ احتياطية تلقائية
- استيراد وتصدير البيانات
- حماية من فقدان البيانات
- تشفير البيانات الحساسة

## اختصارات لوحة المفاتيح

- `Ctrl+S`: حفظ سريع
- `Ctrl+N`: جديد
- `Ctrl+O`: فتح
- `Ctrl+P`: طباعة
- `F1`: المساعدة
- `F11`: ملء الشاشة
- `Escape`: إغلاق النوافذ المنبثقة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### لا تظهر البيانات
- تأكد من دعم المتصفح لـ IndexedDB
- امسح ذاكرة التخزين المؤقت
- تحقق من وحدة التحكم للأخطاء

#### مشاكل في الطباعة
- استخدم Chrome أو Edge للحصول على أفضل نتائج طباعة
- تأكد من إعدادات الطباعة (A4, Portrait)
- فعل "طباعة الخلفيات" في إعدادات الطباعة

#### مشاكل في استيراد Excel
- تأكد من صيغة الملف (.xlsx أو .xls)
- تحقق من وجود الأعمدة المطلوبة
- تأكد من عدم وجود خلايا فارغة في البيانات الأساسية

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير هذا المشروع:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- إنشاء Issue في GitHub
- مراجعة الوثائق والأسئلة الشائعة

## الشكر والتقدير

- شكر خاص لجميع المعلمين والمطورين الذين ساهموا في تطوير هذا النظام
- Font Awesome للأيقونات الرائعة
- Chart.js للرسوم البيانية التفاعلية
- SheetJS لدعم ملفات Excel

---

**تم التطوير بـ ❤️ لخدمة التعليم**
