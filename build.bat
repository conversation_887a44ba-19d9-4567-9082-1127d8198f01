@echo off
chcp 65001 >nul
title بناء تطبيق استمارة تقويم تقنية المعلومات

echo.
echo ========================================
echo    بناء تطبيق استمارة تقويم تقنية المعلومات
echo ========================================
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Node.js
echo.

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من أنك في المجلد الصحيح للمشروع
    pause
    exit /b 1
)

echo 📦 تثبيت التبعيات...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح
echo.

echo 🧹 تنظيف الملفات السابقة...
if exist "dist" rmdir /s /q "dist"
if exist "build\output" rmdir /s /q "build\output"

echo.
echo اختر نوع البناء:
echo 1. بناء للـ Windows (64-bit)
echo 2. بناء للـ Windows (32-bit)
echo 3. بناء للـ Windows (كلاهما)
echo 4. بناء محمول (Portable)
echo 5. بناء جميع الأنواع
echo.

set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" (
    echo 🔨 بناء التطبيق للـ Windows 64-bit...
    call npm run build-win-x64
) else if "%choice%"=="2" (
    echo 🔨 بناء التطبيق للـ Windows 32-bit...
    call npm run build-win-x32
) else if "%choice%"=="3" (
    echo 🔨 بناء التطبيق للـ Windows (كلاهما)...
    call npm run build-win
) else if "%choice%"=="4" (
    echo 🔨 بناء النسخة المحمولة...
    call npx electron-builder --win --portable
) else if "%choice%"=="5" (
    echo 🔨 بناء جميع الأنواع...
    call npm run build-win
) else (
    echo ❌ اختيار غير صحيح
    pause
    exit /b 1
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء التطبيق بنجاح!
echo.

REM عرض الملفات المبنية
if exist "dist" (
    echo 📁 الملفات المبنية موجودة في مجلد dist:
    echo.
    dir /b "dist\*.exe" 2>nul
    dir /b "dist\*.msi" 2>nul
    dir /b "dist\*.zip" 2>nul
    echo.
    
    echo هل تريد فتح مجلد الملفات المبنية؟ (y/n)
    set /p open="اختيارك: "
    if /i "%open%"=="y" (
        start "" "dist"
    )
)

echo.
echo 🎉 انتهى البناء بنجاح!
echo.
echo ملاحظات:
echo - ملف .exe هو المثبت الرئيسي
echo - ملف .zip يحتوي على النسخة المحمولة
echo - يمكنك توزيع أي من هذه الملفات
echo.

pause
