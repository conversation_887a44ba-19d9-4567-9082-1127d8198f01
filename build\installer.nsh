# إعدادات مخصصة للمثبت

# تعيين اللغة العربية
!define MUI_LANGDLL_ALLLANGUAGES

# رسائل مخصصة
LangString WelcomePageTitle ${LANG_ARABIC} "مرحباً بك في معالج تثبيت استمارة تقويم تقنية المعلومات"
LangString WelcomePageText ${LANG_ARABIC} "سيقوم هذا المعالج بإرشادك خلال عملية تثبيت استمارة تقويم تقنية المعلومات.$\r$\n$\r$\nيُنصح بإغلاق جميع التطبيقات الأخرى قبل بدء عملية التثبيت. هذا سيمكن المعالج من تحديث ملفات النظام ذات الصلة دون الحاجة لإعادة تشغيل الكمبيوتر.$\r$\n$\r$\nانقر التالي للمتابعة."

LangString FinishPageTitle ${LANG_ARABIC} "اكتمل تثبيت استمارة تقويم تقنية المعلومات"
LangString FinishPageText ${LANG_ARABIC} "تم تثبيت استمارة تقويم تقنية المعلومات بنجاح على جهاز الكمبيوتر الخاص بك.$\r$\n$\r$\nانقر إنهاء لإغلاق هذا المعالج."

# إعدادات إضافية
!define MUI_WELCOMEPAGE_TITLE $(WelcomePageTitle)
!define MUI_WELCOMEPAGE_TEXT $(WelcomePageText)
!define MUI_FINISHPAGE_TITLE $(FinishPageTitle)
!define MUI_FINISHPAGE_TEXT $(FinishPageText)

# تخصيص أيقونات المثبت
!define MUI_ICON "assets\icon.ico"
!define MUI_UNICON "assets\icon.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "assets\installer-header.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "assets\installer-welcome.bmp"

# إعدادات الصفحات
!define MUI_WELCOMEPAGE_TITLE_3LINES
!define MUI_FINISHPAGE_TITLE_3LINES
!define MUI_FINISHPAGE_RUN "$INSTDIR\${PRODUCT_NAME}.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل استمارة تقويم تقنية المعلومات"

# تحقق من المتطلبات
Function .onInit
    # التحقق من إصدار Windows
    ${IfNot} ${AtLeastWin7}
        MessageBox MB_OK|MB_ICONSTOP "هذا البرنامج يتطلب Windows 7 أو أحدث."
        Abort
    ${EndIf}
    
    # التحقق من وجود .NET Framework
    ReadRegStr $0 HKLM "SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" "Release"
    ${If} $0 == ""
        MessageBox MB_YESNO|MB_ICONQUESTION "هذا البرنامج يتطلب .NET Framework 4.5 أو أحدث. هل تريد تحميله الآن؟" IDYES download IDNO skip
        download:
            ExecShell "open" "https://dotnet.microsoft.com/download/dotnet-framework"
        skip:
    ${EndIf}
FunctionEnd

# إجراءات ما بعد التثبيت
Function .onInstSuccess
    # إنشاء اختصارات إضافية
    CreateShortCut "$DESKTOP\استمارة تقويم تقنية المعلومات.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
    CreateDirectory "$SMPROGRAMS\استمارة تقويم تقنية المعلومات"
    CreateShortCut "$SMPROGRAMS\استمارة تقويم تقنية المعلومات\استمارة تقويم تقنية المعلومات.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
    CreateShortCut "$SMPROGRAMS\استمارة تقويم تقنية المعلومات\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
    
    # تسجيل البرنامج في قائمة البرامج المثبتة
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "DisplayName" "استمارة تقويم تقنية المعلومات"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "DisplayVersion" "${PRODUCT_VERSION}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "Publisher" "IT Evaluation System"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "DisplayIcon" "$INSTDIR\${PRODUCT_NAME}.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "NoRepair" 1
FunctionEnd

# إجراءات إلغاء التثبيت
Function un.onInit
    MessageBox MB_YESNO|MB_ICONQUESTION "هل أنت متأكد من رغبتك في إزالة استمارة تقويم تقنية المعلومات وجميع مكوناتها؟" IDYES +2
    Abort
FunctionEnd

Function un.onUninstSuccess
    # حذف الاختصارات
    Delete "$DESKTOP\استمارة تقويم تقنية المعلومات.lnk"
    RMDir /r "$SMPROGRAMS\استمارة تقويم تقنية المعلومات"
    
    # حذف مفاتيح التسجيل
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
    
    MessageBox MB_OK "تم إلغاء تثبيت استمارة تقويم تقنية المعلومات بنجاح."
FunctionEnd
