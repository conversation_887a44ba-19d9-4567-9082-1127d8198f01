/* أنماط الطباعة الاحترافية */

@media print {
    /* إعدادات عامة للطباعة */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    @page {
        size: A4;
        margin: 1.5cm;
        direction: rtl;
    }

    body {
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        direction: rtl;
    }

    /* إخفاء العناصر غير المطلوبة في الطباعة */
    .main-nav,
    .section-actions,
    .filters-section,
    .action-buttons,
    .btn,
    .modal-overlay,
    .app-footer,
    .no-print {
        display: none !important;
    }

    /* رأس التقرير */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 3px solid #2c3e50;
    }

    .print-header .school-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 15px;
        background: #2c3e50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24pt;
    }

    .print-header h1 {
        font-size: 18pt;
        font-weight: bold;
        color: #2c3e50;
        margin: 10px 0;
    }

    .print-header .school-info {
        font-size: 14pt;
        color: #555;
        margin: 5px 0;
    }

    .print-header .report-info {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        font-size: 11pt;
        color: #666;
    }

    /* عنوان التقرير */
    .report-title {
        text-align: center;
        margin: 20px 0;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid #2c3e50;
        border-radius: 8px;
    }

    .report-title h2 {
        font-size: 16pt;
        font-weight: bold;
        color: #2c3e50;
        margin: 0;
    }

    .report-title .report-details {
        font-size: 12pt;
        color: #555;
        margin-top: 8px;
    }

    /* جداول البيانات */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        font-size: 10pt;
    }

    .data-table th {
        background: #2c3e50 !important;
        color: white !important;
        font-weight: bold;
        padding: 12px 8px;
        text-align: center;
        border: 1px solid #34495e;
    }

    .data-table td {
        padding: 10px 8px;
        text-align: center;
        border: 1px solid #bdc3c7;
        vertical-align: middle;
    }

    .data-table tbody tr:nth-child(even) {
        background: #f8f9fa !important;
    }

    .data-table tbody tr:nth-child(odd) {
        background: white !important;
    }

    /* تلوين الدرجات حسب المستوى */
    .grade-excellent {
        background: #d4edda !important;
        color: #155724 !important;
        font-weight: bold;
    }

    .grade-very-good {
        background: #d1ecf1 !important;
        color: #0c5460 !important;
        font-weight: bold;
    }

    .grade-good {
        background: #fff3cd !important;
        color: #856404 !important;
    }

    .grade-acceptable {
        background: #ffeaa7 !important;
        color: #6c5ce7 !important;
    }

    .grade-weak {
        background: #f8d7da !important;
        color: #721c24 !important;
    }

    /* الإحصائيات */
    .statistics-grid {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin: 20px 0;
    }

    .stat-card {
        border: 2px solid #2c3e50;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        background: #f8f9fa !important;
    }

    .stat-card h4 {
        font-size: 12pt;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .stat-number {
        font-size: 18pt;
        font-weight: bold;
        color: #e74c3c;
    }

    .stat-percentage {
        font-size: 10pt;
        color: #7f8c8d;
        margin-top: 5px;
    }

    /* الرسوم البيانية للطباعة */
    .chart-container {
        page-break-inside: avoid;
        margin: 20px 0;
        text-align: center;
    }

    .chart-title {
        font-size: 14pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 15px;
    }

    /* تذييل التقرير */
    .print-footer {
        display: block !important;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 2px solid #2c3e50;
    }

    .signature-section {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        margin-top: 30px;
    }

    .signature-box {
        text-align: center;
        padding: 20px 10px;
    }

    .signature-line {
        border-bottom: 2px solid #2c3e50;
        margin-bottom: 10px;
        height: 40px;
    }

    .signature-label {
        font-size: 11pt;
        font-weight: bold;
        color: #2c3e50;
    }

    .signature-title {
        font-size: 9pt;
        color: #7f8c8d;
        margin-top: 5px;
    }

    /* معلومات الطباعة */
    .print-info {
        position: fixed;
        bottom: 1cm;
        left: 1cm;
        right: 1cm;
        font-size: 9pt;
        color: #7f8c8d;
        text-align: center;
        border-top: 1px solid #bdc3c7;
        padding-top: 10px;
    }

    /* فواصل الصفحات */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* تنسيق خاص للعناوين */
    h1, h2, h3 {
        color: #2c3e50 !important;
        page-break-after: avoid;
    }

    h1 {
        font-size: 18pt;
        margin: 20px 0 15px;
    }

    h2 {
        font-size: 16pt;
        margin: 18px 0 12px;
    }

    h3 {
        font-size: 14pt;
        margin: 15px 0 10px;
    }

    /* تنسيق الفقرات */
    p {
        margin: 8px 0;
        text-align: justify;
    }

    /* تنسيق القوائم */
    ul, ol {
        margin: 10px 0;
        padding-right: 20px;
    }

    li {
        margin: 5px 0;
    }

    /* تنسيق خاص للدرجات */
    .grade-cell {
        font-weight: bold;
        font-size: 11pt;
    }

    .total-grade {
        background: #2c3e50 !important;
        color: white !important;
        font-weight: bold;
        font-size: 12pt;
    }

    /* تنسيق التقديرات */
    .grade-a { color: #27ae60 !important; font-weight: bold; }
    .grade-b { color: #3498db !important; font-weight: bold; }
    .grade-c { color: #f39c12 !important; font-weight: bold; }
    .grade-d { color: #e67e22 !important; font-weight: bold; }
    .grade-f { color: #e74c3c !important; font-weight: bold; }

    /* تنسيق خاص للملاحظات */
    .notes-section {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
        background: #f8f9fa !important;
    }

    .notes-title {
        font-size: 12pt;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .notes-content {
        font-size: 10pt;
        line-height: 1.6;
        color: #555;
    }

    /* تنسيق التواريخ والأرقام */
    .date, .number {
        font-family: 'Courier New', monospace;
        font-weight: bold;
    }

    /* تحسينات للطباعة الملونة */
    .color-print .grade-excellent { background: #d4edda !important; }
    .color-print .grade-very-good { background: #d1ecf1 !important; }
    .color-print .grade-good { background: #fff3cd !important; }
    .color-print .grade-acceptable { background: #ffeaa7 !important; }
    .color-print .grade-weak { background: #f8d7da !important; }

    /* تحسينات للطباعة بالأبيض والأسود */
    .bw-print .grade-excellent { background: #f0f0f0 !important; border: 2px solid #000; }
    .bw-print .grade-very-good { background: #e8e8e8 !important; border: 1px solid #000; }
    .bw-print .grade-good { background: #e0e0e0 !important; }
    .bw-print .grade-acceptable { background: #d8d8d8 !important; }
    .bw-print .grade-weak { background: #d0d0d0 !important; text-decoration: underline; }
}

/* تنسيق خاص للشاشة عند معاينة الطباعة */
@media screen {
    .print-preview {
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin: 20px auto;
        padding: 2cm;
        max-width: 21cm;
        min-height: 29.7cm;
    }
    
    .print-preview .print-header,
    .print-preview .print-footer {
        display: block;
    }
}
