const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

class ITEvaluationApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.argv.includes('--dev');
        this.init();
    }

    init() {
        // التأكد من أن التطبيق جاهز
        app.whenReady().then(() => {
            this.createMainWindow();
            this.createMenu();
            this.setupIPC();
            
            // إنشاء نافذة جديدة عند النقر على أيقونة التطبيق في macOS
            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // إغلاق التطبيق عند إغلاق جميع النوافذ (إلا في macOS)
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // منع التنقل إلى روابط خارجية
        app.on('web-contents-created', (event, contents) => {
            contents.on('new-window', (navigationEvent, navigationURL) => {
                navigationEvent.preventDefault();
                shell.openExternal(navigationURL);
            });
        });
    }

    createMainWindow() {
        // إنشاء النافذة الرئيسية
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1000,
            minHeight: 700,
            icon: path.join(__dirname, '../assets/icon.png'),
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true,
                webSecurity: false
            },
            show: false, // لا تظهر النافذة حتى تكون جاهزة
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
            backgroundColor: '#f5f6fa'
        });

        // تحميل الملف الرئيسي
        const indexPath = path.join(__dirname, '../index.html');
        this.mainWindow.loadFile(indexPath);

        // إظهار النافذة عند الانتهاء من التحميل
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // فتح أدوات المطور في بيئة التطوير
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // التعامل مع إغلاق النافذة
        this.mainWindow.on('close', (event) => {
            // يمكن إضافة تحذير للحفظ هنا
            const choice = dialog.showMessageBoxSync(this.mainWindow, {
                type: 'question',
                buttons: ['نعم', 'لا'],
                defaultId: 0,
                message: 'هل تريد إغلاق التطبيق؟',
                detail: 'تأكد من حفظ جميع البيانات قبل الإغلاق.'
            });

            if (choice === 1) {
                event.preventDefault();
            }
        });

        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }

    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'جديد',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow.webContents.send('menu-new');
                        }
                    },
                    {
                        label: 'فتح',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => {
                            this.openFile();
                        }
                    },
                    {
                        label: 'حفظ',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => {
                            this.mainWindow.webContents.send('menu-save');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'استيراد من Excel',
                        click: () => {
                            this.importExcel();
                        }
                    },
                    {
                        label: 'تصدير إلى Excel',
                        click: () => {
                            this.exportExcel();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'نسخة احتياطية',
                        click: () => {
                            this.createBackup();
                        }
                    },
                    {
                        label: 'استعادة من نسخة احتياطية',
                        click: () => {
                            this.restoreBackup();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'طباعة',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => {
                            this.mainWindow.webContents.print();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    {
                        label: 'تراجع',
                        accelerator: 'CmdOrCtrl+Z',
                        role: 'undo'
                    },
                    {
                        label: 'إعادة',
                        accelerator: 'Shift+CmdOrCtrl+Z',
                        role: 'redo'
                    },
                    { type: 'separator' },
                    {
                        label: 'قص',
                        accelerator: 'CmdOrCtrl+X',
                        role: 'cut'
                    },
                    {
                        label: 'نسخ',
                        accelerator: 'CmdOrCtrl+C',
                        role: 'copy'
                    },
                    {
                        label: 'لصق',
                        accelerator: 'CmdOrCtrl+V',
                        role: 'paste'
                    },
                    {
                        label: 'تحديد الكل',
                        accelerator: 'CmdOrCtrl+A',
                        role: 'selectall'
                    }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    {
                        label: 'إعادة تحميل',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            this.mainWindow.reload();
                        }
                    },
                    {
                        label: 'إعادة تحميل قسري',
                        accelerator: 'CmdOrCtrl+Shift+R',
                        click: () => {
                            this.mainWindow.webContents.reloadIgnoringCache();
                        }
                    },
                    {
                        label: 'أدوات المطور',
                        accelerator: 'F12',
                        click: () => {
                            this.mainWindow.webContents.toggleDevTools();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'تكبير',
                        accelerator: 'CmdOrCtrl+Plus',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() + 1
                            );
                        }
                    },
                    {
                        label: 'تصغير',
                        accelerator: 'CmdOrCtrl+-',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() - 1
                            );
                        }
                    },
                    {
                        label: 'الحجم الطبيعي',
                        accelerator: 'CmdOrCtrl+0',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(0);
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'ملء الشاشة',
                        accelerator: 'F11',
                        click: () => {
                            this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen());
                        }
                    }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول التطبيق',
                        click: () => {
                            this.showAbout();
                        }
                    },
                    {
                        label: 'دليل المستخدم',
                        click: () => {
                            this.showUserGuide();
                        }
                    },
                    {
                        label: 'الدعم الفني',
                        click: () => {
                            shell.openExternal('mailto:<EMAIL>');
                        }
                    }
                ]
            }
        ];

        // إضافة قائمة خاصة بـ macOS
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    {
                        label: 'حول ' + app.getName(),
                        role: 'about'
                    },
                    { type: 'separator' },
                    {
                        label: 'الخدمات',
                        role: 'services',
                        submenu: []
                    },
                    { type: 'separator' },
                    {
                        label: 'إخفاء ' + app.getName(),
                        accelerator: 'Command+H',
                        role: 'hide'
                    },
                    {
                        label: 'إخفاء الآخرين',
                        accelerator: 'Command+Shift+H',
                        role: 'hideothers'
                    },
                    {
                        label: 'إظهار الكل',
                        role: 'unhide'
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: 'Command+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupIPC() {
        // التعامل مع رسائل IPC من العملية المرسلة
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-message-box', async (event, options) => {
            const result = await dialog.showMessageBox(this.mainWindow, options);
            return result;
        });
    }

    async openFile() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'فتح ملف',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('file-opened', result.filePaths[0]);
        }
    }

    async importExcel() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'استيراد من Excel',
            filters: [
                { name: 'ملفات Excel', extensions: ['xlsx', 'xls'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('excel-import', result.filePaths[0]);
        }
    }

    async exportExcel() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'تصدير إلى Excel',
            defaultPath: `تقرير_${new Date().toISOString().split('T')[0]}.xlsx`,
            filters: [
                { name: 'ملفات Excel', extensions: ['xlsx'] }
            ]
        });

        if (!result.canceled) {
            this.mainWindow.webContents.send('excel-export', result.filePath);
        }
    }

    async createBackup() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'إنشاء نسخة احتياطية',
            defaultPath: `backup_${new Date().toISOString().split('T')[0]}.json`,
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] }
            ]
        });

        if (!result.canceled) {
            this.mainWindow.webContents.send('create-backup', result.filePath);
        }
    }

    async restoreBackup() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'استعادة من نسخة احتياطية',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.mainWindow.webContents.send('restore-backup', result.filePaths[0]);
        }
    }

    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'حول التطبيق',
            message: 'استمارة تقويم تقنية المعلومات',
            detail: `الإصدار: ${app.getVersion()}\n\nنظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات\n\nتم التطوير بواسطة: فريق تطوير التطبيقات التعليمية`,
            buttons: ['موافق']
        });
    }

    showUserGuide() {
        // يمكن فتح ملف PDF أو صفحة ويب للدليل
        this.mainWindow.webContents.send('show-user-guide');
    }
}

// إنشاء مثيل من التطبيق
new ITEvaluationApp();
