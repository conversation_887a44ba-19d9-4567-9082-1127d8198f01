// إدارة التقارير والإحصائيات
class ReportsManager {
    constructor() {
        this.chartInstances = {};
        this.init();
    }

    init() {
        // تحميل مكتبة Chart.js إذا لم تكن محملة
        this.loadChartJS();
    }

    loadChartJS() {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            document.head.appendChild(script);
        }
    }

    async generateSemesterReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('semester');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectSemesterData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateSemesterReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');
            
            showNotification('تم إنشاء التقرير الفصلي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير الفصلي:', error);
            showNotification('خطأ في إنشاء التقرير الفصلي', 'error');
        }
    }

    async generateAnnualReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('annual');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectAnnualData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateAnnualReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');
            
            showNotification('تم إنشاء التقرير السنوي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير السنوي:', error);
            showNotification('خطأ في إنشاء التقرير السنوي', 'error');
        }
    }

    showReportCriteriaModal(reportType) {
        return new Promise((resolve) => {
            const modalContent = `
                <div class="modal-header">
                    <h3>${reportType === 'semester' ? 'معايير التقرير الفصلي' : 'معايير التقرير السنوي'}</h3>
                    <button class="btn btn-sm btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="report-criteria-form" class="criteria-form">
                    <div class="form-group">
                        <label for="report-academic-year">العام الدراسي:</label>
                        <select id="report-academic-year" required>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                        </select>
                    </div>
                    
                    ${reportType === 'semester' ? `
                    <div class="form-group">
                        <label for="report-semester">الفصل الدراسي:</label>
                        <select id="report-semester" required>
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                    </div>
                    ` : ''}
                    
                    <div class="form-group">
                        <label for="report-grade">الصف (اختياري):</label>
                        <select id="report-grade">
                            <option value="">جميع الصفوف</option>
                            <option value="1">الصف الأول</option>
                            <option value="2">الصف الثاني</option>
                            <option value="3">الصف الثالث</option>
                            <option value="4">الصف الرابع</option>
                            <option value="5">الصف الخامس</option>
                            <option value="6">الصف السادس</option>
                            <option value="7">الصف السابع</option>
                            <option value="8">الصف الثامن</option>
                            <option value="9">الصف التاسع</option>
                            <option value="10">الصف العاشر</option>
                            <option value="11">الصف الحادي عشر</option>
                            <option value="12">الصف الثاني عشر</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-section">الشعبة (اختياري):</label>
                        <select id="report-section">
                            <option value="">جميع الشعب</option>
                            <option value="أ">أ</option>
                            <option value="ب">ب</option>
                            <option value="ج">ج</option>
                            <option value="د">د</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-alt"></i> إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            `;

            showModal(modalContent);

            document.getElementById('report-criteria-form').addEventListener('submit', (e) => {
                e.preventDefault();
                
                const criteria = {
                    academic_year: document.getElementById('report-academic-year').value,
                    grade: document.getElementById('report-grade').value,
                    section: document.getElementById('report-section').value
                };

                if (reportType === 'semester') {
                    criteria.semester = document.getElementById('report-semester').value;
                }

                closeModal();
                resolve(criteria);
            });
        });
    }

    async collectSemesterData(criteria) {
        const filters = {
            academic_year: criteria.academic_year,
            semester: criteria.semester
        };

        if (criteria.grade) filters.grade_level = criteria.grade;

        const grades = await dbManager.getGrades(filters);
        const students = await dbManager.getStudents({
            grade: criteria.grade,
            section: criteria.section
        });

        const statistics = await dbManager.getStatistics(filters);

        return {
            grades,
            students,
            statistics,
            criteria
        };
    }

    async collectAnnualData(criteria) {
        const semester1Data = await this.collectSemesterData({
            ...criteria,
            semester: '1'
        });

        const semester2Data = await this.collectSemesterData({
            ...criteria,
            semester: '2'
        });

        return {
            semester1: semester1Data,
            semester2: semester2Data,
            criteria
        };
    }

    generateSemesterReportHTML(data, criteria) {
        const { grades, students, statistics } = data;
        
        return `
            <div class="print-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="school-info">التقرير الفصلي</div>
                <div class="report-info">
                    <span>العام الدراسي: ${criteria.academic_year}</span>
                    <span>الفصل: ${criteria.semester === '1' ? 'الأول' : 'الثاني'}</span>
                    <span>تاريخ التقرير: ${formatDate(new Date())}</span>
                </div>
            </div>

            <div class="report-title">
                <h2>التقرير الفصلي - ${criteria.semester === '1' ? 'الفصل الأول' : 'الفصل الثاني'}</h2>
                <div class="report-details">
                    العام الدراسي: ${criteria.academic_year}
                    ${criteria.grade ? ` - الصف: ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة: ${criteria.section}` : ''}
                </div>
            </div>

            <div class="statistics-section">
                <h3>الإحصائيات العامة</h3>
                <div class="statistics-grid">
                    <div class="stat-card">
                        <h4>إجمالي الطلاب</h4>
                        <div class="stat-number">${statistics.total_students}</div>
                    </div>
                    <div class="stat-card">
                        <h4>المتوسط العام</h4>
                        <div class="stat-number">${statistics.average_score.toFixed(2)}</div>
                    </div>
                    <div class="stat-card">
                        <h4>نسبة النجاح</h4>
                        <div class="stat-number">${statistics.pass_rate.toFixed(1)}%</div>
                    </div>
                </div>
            </div>

            <div class="grade-distribution-section">
                <h3>توزيع الدرجات</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>التقدير</th>
                            <th>عدد الطلاب</th>
                            <th>النسبة المئوية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="grade-excellent">
                            <td>ممتاز (أ)</td>
                            <td>${statistics.grade_distribution.excellent}</td>
                            <td>${((statistics.grade_distribution.excellent / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-very-good">
                            <td>جيد جداً (ب)</td>
                            <td>${statistics.grade_distribution.very_good}</td>
                            <td>${((statistics.grade_distribution.very_good / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-good">
                            <td>جيد (ج)</td>
                            <td>${statistics.grade_distribution.good}</td>
                            <td>${((statistics.grade_distribution.good / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-acceptable">
                            <td>مقبول (د)</td>
                            <td>${statistics.grade_distribution.acceptable}</td>
                            <td>${((statistics.grade_distribution.acceptable / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                        <tr class="grade-weak">
                            <td>ضعيف (هـ)</td>
                            <td>${statistics.grade_distribution.weak}</td>
                            <td>${((statistics.grade_distribution.weak / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="detailed-grades-section">
                <h3>تفاصيل الدرجات</h3>
                ${this.generateDetailedGradesTable(grades, students)}
            </div>

            <div class="print-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس القسم</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button class="btn btn-info" onclick="reportsManager.exportReportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
            </div>
        `;
    }

    generateAnnualReportHTML(data, criteria) {
        const { semester1, semester2 } = data;
        
        return `
            <div class="print-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="school-info">التقرير السنوي</div>
                <div class="report-info">
                    <span>العام الدراسي: ${criteria.academic_year}</span>
                    <span>تاريخ التقرير: ${formatDate(new Date())}</span>
                </div>
            </div>

            <div class="report-title">
                <h2>التقرير السنوي</h2>
                <div class="report-details">
                    العام الدراسي: ${criteria.academic_year}
                    ${criteria.grade ? ` - الصف: ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة: ${criteria.section}` : ''}
                </div>
            </div>

            <div class="annual-comparison">
                <h3>مقارنة الفصلين الدراسيين</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                            <th>التحسن</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>عدد الطلاب</td>
                            <td>${semester1.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students - semester1.statistics.total_students}</td>
                        </tr>
                        <tr>
                            <td>المتوسط العام</td>
                            <td>${semester1.statistics.average_score.toFixed(2)}</td>
                            <td>${semester2.statistics.average_score.toFixed(2)}</td>
                            <td>${(semester2.statistics.average_score - semester1.statistics.average_score).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <td>نسبة النجاح</td>
                            <td>${semester1.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${semester2.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${(semester2.statistics.pass_rate - semester1.statistics.pass_rate).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="semester-details">
                <div class="semester-section">
                    <h3>الفصل الدراسي الأول</h3>
                    ${this.generateSemesterSummary(semester1)}
                </div>
                
                <div class="semester-section">
                    <h3>الفصل الدراسي الثاني</h3>
                    ${this.generateSemesterSummary(semester2)}
                </div>
            </div>

            <div class="print-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس القسم</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button class="btn btn-info" onclick="reportsManager.exportReportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
            </div>
        `;
    }

    generateSemesterSummary(semesterData) {
        const { statistics } = semesterData;
        
        return `
            <div class="semester-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الطلاب:</span>
                        <span class="stat-value">${statistics.total_students}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">المتوسط العام:</span>
                        <span class="stat-value">${statistics.average_score.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">نسبة النجاح:</span>
                        <span class="stat-value">${statistics.pass_rate.toFixed(1)}%</span>
                    </div>
                </div>
                
                <div class="grade-breakdown">
                    <div class="grade-item grade-excellent">
                        <span>ممتاز: ${statistics.grade_distribution.excellent}</span>
                    </div>
                    <div class="grade-item grade-very-good">
                        <span>جيد جداً: ${statistics.grade_distribution.very_good}</span>
                    </div>
                    <div class="grade-item grade-good">
                        <span>جيد: ${statistics.grade_distribution.good}</span>
                    </div>
                    <div class="grade-item grade-acceptable">
                        <span>مقبول: ${statistics.grade_distribution.acceptable}</span>
                    </div>
                    <div class="grade-item grade-weak">
                        <span>ضعيف: ${statistics.grade_distribution.weak}</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateDetailedGradesTable(grades, students) {
        if (grades.length === 0) {
            return '<p class="text-center">لا توجد درجات مسجلة</p>';
        }

        let tableHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الطالب</th>
                        <th>المجموع</th>
                        <th>المستوى</th>
                        <th>التقدير</th>
                    </tr>
                </thead>
                <tbody>
        `;

        grades.forEach(grade => {
            const student = students.find(s => s.id === grade.student_id);
            if (student) {
                tableHTML += `
                    <tr class="${this.getGradeCSSClass(grade.level)}">
                        <td>${student.student_number}</td>
                        <td>${student.name}</td>
                        <td>${grade.total.toFixed(2)}</td>
                        <td>${grade.level}</td>
                        <td>${grade.descriptive_phrase}</td>
                    </tr>
                `;
            }
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getGradeCSSClass(level) {
        if (level === 'أ' || level === '1') return 'grade-excellent';
        if (level === 'ب' || level === '2') return 'grade-very-good';
        if (level === 'ج' || level === '3') return 'grade-good';
        if (level === 'د' || level === '4') return 'grade-acceptable';
        return 'grade-weak';
    }

    async showStatistics() {
        try {
            const statistics = await dbManager.getStatistics();
            const reportContent = document.getElementById('report-content');
            
            const statisticsHTML = `
                <div class="statistics-dashboard">
                    <h2>الإحصائيات والتحليلات</h2>
                    
                    <div class="stats-overview">
                        <div class="stat-card">
                            <h3>إجمالي الطلاب</h3>
                            <div class="stat-number">${statistics.total_students}</div>
                        </div>
                        <div class="stat-card">
                            <h3>المتوسط العام</h3>
                            <div class="stat-number">${statistics.average_score.toFixed(2)}</div>
                        </div>
                        <div class="stat-card">
                            <h3>نسبة النجاح</h3>
                            <div class="stat-number">${statistics.pass_rate.toFixed(1)}%</div>
                        </div>
                    </div>
                    
                    <div class="charts-section">
                        <div class="chart-container">
                            <canvas id="gradeDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            `;
            
            reportContent.innerHTML = statisticsHTML;
            
            // إنشاء الرسم البياني
            this.createGradeDistributionChart(statistics.grade_distribution);
            
            showSection('reports');
            showNotification('تم تحميل الإحصائيات بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
            showNotification('خطأ في تحميل الإحصائيات', 'error');
        }
    }

    createGradeDistributionChart(distribution) {
        const ctx = document.getElementById('gradeDistributionChart');
        if (!ctx) return;

        if (this.chartInstances.gradeDistribution) {
            this.chartInstances.gradeDistribution.destroy();
        }

        this.chartInstances.gradeDistribution = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    data: [
                        distribution.excellent,
                        distribution.very_good,
                        distribution.good,
                        distribution.acceptable,
                        distribution.weak
                    ],
                    backgroundColor: [
                        '#27ae60',
                        '#3498db',
                        '#f39c12',
                        '#e67e22',
                        '#e74c3c'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع الدرجات'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    showCharts() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showNotification('سيتم إضافة المزيد من الرسوم البيانية قريباً', 'info');
    }

    exportReportToPDF() {
        window.print();
    }

    exportReportToExcel() {
        const reportContent = document.getElementById('report-content');
        const tables = reportContent.querySelectorAll('table');
        
        if (tables.length === 0) {
            showNotification('لا توجد جداول للتصدير', 'warning');
            return;
        }

        const wb = XLSX.utils.book_new();
        
        tables.forEach((table, index) => {
            const ws = XLSX.utils.table_to_sheet(table);
            XLSX.utils.book_append_sheet(wb, ws, `جدول_${index + 1}`);
        });

        XLSX.writeFile(wb, `تقرير_${new Date().toISOString().split('T')[0]}.xlsx`);
        showNotification('تم تصدير التقرير إلى Excel بنجاح', 'success');
    }
}

// إنشاء مثيل من مدير التقارير
const reportsManager = new ReportsManager();

// دوال مساعدة
function generateSemesterReport() {
    reportsManager.generateSemesterReport();
}

function generateAnnualReport() {
    reportsManager.generateAnnualReport();
}

function showStatistics() {
    reportsManager.showStatistics();
}

function showCharts() {
    reportsManager.showCharts();
}
