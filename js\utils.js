// الوظائف المساعدة والأدوات العامة

// تنسيق التاريخ
function formatDate(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

// تنسيق التاريخ بالعربية
function formatDateArabic(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    return `${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`;
}

// تنسيق الوقت
function formatTime(date) {
    if (!date) return '';
    
    const d = new Date(date);
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    
    return `${hours}:${minutes}`;
}

// إظهار الإشعارات
function showNotification(message, type = 'info', duration = 3000) {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // إنشاء الإشعار الجديد
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد المدة المحددة
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, duration);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

// إظهار النافذة المنبثقة
function showModal(content) {
    const modalOverlay = document.getElementById('modal-overlay');
    const modalContent = document.getElementById('modal-content');
    
    if (modalOverlay && modalContent) {
        modalContent.innerHTML = content;
        modalOverlay.classList.add('active');
        
        // إضافة مستمع لإغلاق النافذة عند النقر خارجها
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });
    }
}

// إغلاق النافذة المنبثقة
function closeModal() {
    const modalOverlay = document.getElementById('modal-overlay');
    if (modalOverlay) {
        modalOverlay.classList.remove('active');
    }
}

// إظهار قسم معين
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));

    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // تحديث حالة الروابط في القائمة
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));

    const activeLink = document.querySelector(`[data-section="${sectionId}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// التحقق من صحة البيانات
function validateRequired(value, fieldName) {
    if (!value || value.trim() === '') {
        throw new Error(`${fieldName} مطلوب`);
    }
    return true;
}

function validateNumber(value, fieldName, min = null, max = null) {
    const num = parseFloat(value);
    if (isNaN(num)) {
        throw new Error(`${fieldName} يجب أن يكون رقماً صحيحاً`);
    }
    if (min !== null && num < min) {
        throw new Error(`${fieldName} يجب أن يكون أكبر من أو يساوي ${min}`);
    }
    if (max !== null && num > max) {
        throw new Error(`${fieldName} يجب أن يكون أقل من أو يساوي ${max}`);
    }
    return true;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        throw new Error('البريد الإلكتروني غير صحيح');
    }
    return true;
}

// تحويل الأرقام إلى العربية
function toArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.toString().replace(/[0-9]/g, (match) => arabicNumbers[parseInt(match)]);
}

// تحويل الأرقام إلى الإنجليزية
function toEnglishNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    let result = str.toString();
    arabicNumbers.forEach((arabic, index) => {
        result = result.replace(new RegExp(arabic, 'g'), index.toString());
    });
    return result;
}

// تنسيق الأرقام بالفواصل
function formatNumber(number, decimals = 2) {
    if (isNaN(number)) return '0';
    return parseFloat(number).toLocaleString('ar-SA', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

// تحويل النص إلى عنوان URL صالح
function slugify(text) {
    return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');
}

// نسخ النص إلى الحافظة
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showNotification('تم نسخ النص إلى الحافظة', 'success');
    } catch (err) {
        console.error('فشل في نسخ النص:', err);
        showNotification('فشل في نسخ النص', 'error');
    }
}

// تحميل ملف
function downloadFile(content, filename, contentType = 'text/plain') {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

// قراءة ملف
function readFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsText(file);
    });
}

// تحويل البيانات إلى JSON
function exportToJSON(data, filename) {
    const jsonString = JSON.stringify(data, null, 2);
    downloadFile(jsonString, filename, 'application/json');
}

// استيراد البيانات من JSON
async function importFromJSON(file) {
    try {
        const content = await readFile(file);
        return JSON.parse(content);
    } catch (error) {
        throw new Error('فشل في قراءة ملف JSON');
    }
}

// تحويل جدول HTML إلى CSV
function tableToCSV(table) {
    const rows = table.querySelectorAll('tr');
    const csvContent = [];

    rows.forEach(row => {
        const cells = row.querySelectorAll('th, td');
        const rowData = Array.from(cells).map(cell => {
            let text = cell.textContent.trim();
            // إضافة علامات اقتباس إذا كان النص يحتوي على فواصل
            if (text.includes(',') || text.includes('"') || text.includes('\n')) {
                text = '"' + text.replace(/"/g, '""') + '"';
            }
            return text;
        });
        csvContent.push(rowData.join(','));
    });

    return csvContent.join('\n');
}

// تصدير جدول إلى CSV
function exportTableToCSV(table, filename) {
    const csv = tableToCSV(table);
    downloadFile(csv, filename, 'text/csv');
}

// البحث في النص
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// تنظيف النص
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// تحويل الحجم بالبايت إلى وحدة قابلة للقراءة
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// التحقق من دعم المتصفح لميزة معينة
function isFeatureSupported(feature) {
    const features = {
        localStorage: () => typeof Storage !== 'undefined',
        indexedDB: () => 'indexedDB' in window,
        webWorkers: () => typeof Worker !== 'undefined',
        canvas: () => {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && canvas.getContext('2d'));
        },
        webGL: () => {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && canvas.getContext('webgl'));
        }
    };
    
    return features[feature] ? features[feature]() : false;
}

// إنشاء معرف فريد
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// تأخير التنفيذ
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// تجميع الاستدعاءات المتكررة
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تحديد معدل الاستدعاءات
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// التحقق من كون الجهاز محمولاً
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// التحقق من كون الجهاز لوحياً
function isTablet() {
    return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
}

// الحصول على معلومات المتصفح
function getBrowserInfo() {
    const ua = navigator.userAgent;
    let browser = 'Unknown';
    
    if (ua.includes('Chrome')) browser = 'Chrome';
    else if (ua.includes('Firefox')) browser = 'Firefox';
    else if (ua.includes('Safari')) browser = 'Safari';
    else if (ua.includes('Edge')) browser = 'Edge';
    else if (ua.includes('Opera')) browser = 'Opera';
    
    return {
        name: browser,
        userAgent: ua,
        language: navigator.language,
        platform: navigator.platform
    };
}

// تحديث الوقت والتاريخ في الرأس
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');
    
    if (dateElement) {
        dateElement.textContent = formatDateArabic(now);
    }
    
    if (timeElement) {
        timeElement.textContent = formatTime(now);
    }
}

// تشغيل تحديث الوقت كل دقيقة
setInterval(updateDateTime, 60000);

// تحديث الوقت عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateDateTime);
