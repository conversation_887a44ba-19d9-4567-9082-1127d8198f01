{"name": "it-evaluation-system", "version": "1.0.0", "description": "استمارة تقويم تقنية المعلومات - نظام شامل لإدارة درجات الطلاب", "main": "electron/main.js", "homepage": "./", "author": {"name": "IT Evaluation System", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["education", "grades", "students", "evaluation", "school", "arabic", "rtl"], "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win --x64 --ia32", "build-win-x64": "electron-builder --win --x64", "build-win-x32": "electron-builder --win --ia32", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "dist-win": "npm run build-win", "dist-all": "electron-builder --win --mac --linux", "postinstall": "electron-builder install-app-deps", "serve": "http-server . -p 8080 -o", "clean": "<PERSON><PERSON><PERSON> dist build", "rebuild": "npm run clean && npm install && npm run build", "test": "echo \"No tests specified\" && exit 0", "release": "electron-builder --publish=always"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "http-server": "^14.1.1", "rimraf": "^5.0.5"}, "dependencies": {"electron-updater": "^6.1.4", "electron-store": "^8.1.0"}, "build": {"appId": "com.itevaluation.app", "productName": "استمارة تقويم تقنية المعلومات", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!.git/**/*", "!.vscode/**/*", "!*.md", "!.giti<PERSON>re"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}, {"target": "zip", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "IT Evaluation System", "verifyUpdateCodeSignature": false, "certificateFile": null, "certificatePassword": null}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.education", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Education", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "استمارة تقويم تقنية المعلومات", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "artifactName": "${productName}-Setup-${version}.${ext}", "language": "1025", "include": "build/installer.nsh", "warningsAsErrors": false, "displayLanguageSelector": false, "installerLanguages": ["Arabic"], "uninstallerLanguages": ["Arabic"], "license": "LICENSE", "welcomeTitle": "مرحباً بك في برنامج استمارة تقويم تقنية المعلومات", "completedTitle": "تم تثبيت البرنامج بنجاح"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "publish": {"provider": "github", "owner": "your-username", "repo": "it-evaluation-system"}}, "repository": {"type": "git", "url": "https://github.com/your-username/it-evaluation-system.git"}, "bugs": {"url": "https://github.com/your-username/it-evaluation-system/issues"}}