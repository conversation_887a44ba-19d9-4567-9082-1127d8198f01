@echo off
chcp 65001 >nul
title استمارة تقويم تقنية المعلومات

echo.
echo ========================================
echo    استمارة تقويم تقنية المعلومات
echo ========================================
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org
    echo.
    echo أو يمكنك فتح ملف index.html مباشرة في المتصفح
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Node.js
echo.

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من أنك في المجلد الصحيح للمشروع
    pause
    exit /b 1
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo 🚀 بدء تشغيل التطبيق...
echo.
echo سيتم فتح التطبيق في نافذة منفصلة...
echo لإغلاق التطبيق، أغلق هذه النافذة أو اضغط Ctrl+C
echo.

REM تشغيل التطبيق
call npm start

echo.
echo تم إغلاق التطبيق
pause
